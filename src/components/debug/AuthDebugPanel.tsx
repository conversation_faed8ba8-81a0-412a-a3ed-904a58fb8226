import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthDebugInfo, validateAuthSetup, checkCorsIssues } from '@/utils/auth-debug';

/**
 * Debug panel to help diagnose authentication issues
 */
export const AuthDebugPanel: React.FC = () => {
  const { accessToken, user, refreshSession } = useAuth();
  
  const debugInfo = getAuthDebugInfo(accessToken);
  const authValidation = validateAuthSetup(accessToken);
  const corsCheck = checkCorsIssues();
  
  const handleRefreshSession = async () => {
    try {
      await refreshSession();
      console.log('Session refresh triggered from debug panel');
    } catch (error) {
      console.error('Session refresh failed:', error);
    }
  };
  
  return (
    <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800 space-y-4">
      <h3 className="text-lg font-semibold">🔐 Authentication Debug Panel</h3>
      
      {/* User Info */}
      <div>
        <h4 className="font-medium mb-2">User Information</h4>
        <div className="text-sm space-y-1">
          <div>Signed In: {user ? '✅ Yes' : '❌ No'}</div>
          {user && (
            <>
              <div>User ID: {user.id}</div>
              <div>Email: {user.email}</div>
            </>
          )}
        </div>
      </div>
      
      {/* Token Info */}
      <div>
        <h4 className="font-medium mb-2">Token Information</h4>
        <div className="text-sm space-y-1">
          <div>Has Access Token: {debugInfo.hasAccessToken ? '✅ Yes' : '❌ No'}</div>
          {debugInfo.hasAccessToken && (
            <div>Token Length: {debugInfo.accessTokenLength} characters</div>
          )}
        </div>
      </div>
      
      {/* Cookie Info */}
      <div>
        <h4 className="font-medium mb-2">Cookie Information</h4>
        <div className="text-sm space-y-1">
          <div>Has Session Cookie: {debugInfo.hasSessionCookie ? '✅ Yes' : '❌ No'}</div>
          <div>Available Cookies: {debugInfo.cookieNames.join(', ') || 'None'}</div>
        </div>
      </div>
      
      {/* CORS Check */}
      <div>
        <h4 className="font-medium mb-2">CORS Check</h4>
        <div className="text-sm space-y-1">
          <div>CORS Issues: {corsCheck.hasCorsIssues ? '⚠️ Yes' : '✅ No'}</div>
          {corsCheck.reason && <div>Reason: {corsCheck.reason}</div>}
        </div>
      </div>
      
      {/* Validation Results */}
      <div>
        <h4 className="font-medium mb-2">Validation Results</h4>
        <div className="text-sm space-y-1">
          <div>Auth Setup Valid: {authValidation.isValid ? '✅ Yes' : '❌ No'}</div>
          {authValidation.issues.length > 0 && (
            <div>
              <div className="font-medium text-red-600">Issues:</div>
              <ul className="list-disc list-inside ml-2">
                {authValidation.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
          {authValidation.recommendations.length > 0 && (
            <div>
              <div className="font-medium text-blue-600">Recommendations:</div>
              <ul className="list-disc list-inside ml-2">
                {authValidation.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
      
      {/* Actions */}
      <div>
        <h4 className="font-medium mb-2">Actions</h4>
        <div className="space-x-2">
          <button
            onClick={handleRefreshSession}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            Refresh Session
          </button>
          <button
            onClick={() => console.log('Current auth state:', { accessToken, user, debugInfo })}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            Log Auth State
          </button>
        </div>
      </div>
      
      {/* Environment Info */}
      <div>
        <h4 className="font-medium mb-2">Environment</h4>
        <div className="text-sm space-y-1">
          <div>Current URL: {debugInfo.currentUrl}</div>
          <div>API URL: {(import.meta as any).env?.VITE_API_URL || 'http://localhost:3001'}</div>
          <div>Timestamp: {debugInfo.timestamp}</div>
        </div>
      </div>
    </div>
  );
};
